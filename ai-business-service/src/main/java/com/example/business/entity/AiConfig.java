package com.example.business.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import com.example.common.entity.BaseEntity;

import java.util.Date;

/**
 * AI配置对象 ai_config
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Accessors(chain=true)
public class AiConfig
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 配置键 */

    private String configKey;

    /** 配置值 */

    private String configValue;

    /** 配置类型 */

    private String configType;

    /** 配置描述 */

    private String description;

    /** 配置分类 */

    private String category;

    /** 是否加密：0-否，1-是 */

    private Long isEncrypted;

    /** 是否启用：0-禁用，1-启用 */

    private Long isActive;

    private Date createTime;

    private Date updateTime;

}

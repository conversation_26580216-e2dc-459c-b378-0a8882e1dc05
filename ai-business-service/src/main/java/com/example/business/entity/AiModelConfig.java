package com.example.business.entity;

import java.math.BigDecimal;
import java.util.Date;

import com.example.common.entity.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * AI模型配置对象 ai_model_config
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Accessors(chain=true)
public class AiModelConfig
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 模型名称 */

    private String modelName;

    /** 模型别名（显示名称） */

    private String modelAlias;

    /** 模型类型：chat-对话，completion-补全，embedding-嵌入，image-图像 */

    private String modelType;

    /** 提供商：openai,anthropic,alibaba,baidu,zhipu等 */

    private String provider;

    /** API端点URL */

    private String apiEndpoint;

    /** API版本 */

    private String apiVersion;

    /** 最大Token数 */

    private Long maxTokens;

    /** 最大上下文长度 */

    private Long maxContextLength;

    /** 每1K输入Token费用（美元） */

    private BigDecimal costPerInput;

    /** 每1K输出Token费用（美元） */

    private BigDecimal costPerOutput;

    /** 是否支持流式响应：0-不支持，1-支持 */

    private Long supportsStreaming;

    /** 是否支持函数调用：0-不支持，1-支持 */

    private Long supportsFunctionCall;

    /** 是否启用：0-禁用，1-启用 */

    private Long isEnabled;

    /** 优先级（数字越大优先级越高） */

    private Long priority;

    /** 每分钟请求限制 */

    private Long rateLimitRpm;

    /** 每分钟Token限制 */

    private Long rateLimitTpm;

    /** 模型描述 */

    private String description;

    /** 模型配置JSON（温度、top_p等参数） */

    private String configJson;

    private Date createTime;

    private Date updateTime;


}

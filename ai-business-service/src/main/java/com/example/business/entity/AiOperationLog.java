package com.example.business.entity;


import com.example.common.entity.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * AI系统操作日志对象 ai_operation_log
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Accessors(chain=true)
public class  AiOperationLog
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 用户ID */

    private Long userId;

    /** 会话ID */

    private String sessionId;

    /** 操作类型：chat,config_change,quota_update,model_switch等 */

    private String operationType;

    /** 操作描述 */

    private String operationDesc;

    /** 请求数据 */

    private String requestData;

    /** 响应数据 */

    private String responseData;

    /** IP地址 */

    private String ipAddress;

    /** 用户代理 */

    private String userAgent;

    /** 状态：1-成功，2-失败，3-部分成功 */

    private Long status;

    /** 错误代码 */

    private String errorCode;

    /** 错误信息 */

    private String errorMessage;

    /** 执行时间（毫秒） */

    private Long executionTime;

    /** 链路追踪ID */

    private String traceId;

    private Date createTime;

    private Date updateTime;

}

package com.example.business.entity;

import java.math.BigDecimal;
import java.util.Date;

import com.example.common.entity.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * AI会话模板对象 ai_chat_template
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Accessors(chain=true)
public class AiChatTemplate
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 模板名称 */

    private String templateName;

    /** 模板代码（唯一标识） */

    private String templateCode;

    /** 模板类型：system-系统，user-用户自定义 */

    private String templateType;

    /** 分类：general-通用，coding-编程，writing-写作，analysis-分析等 */

    private String category;

    /** 系统提示词 */

    private String systemPrompt;

    /** 用户提示词模板（支持变量替换） */

    private String userPromptTemplate;

    /** 模型配置（温度、最大Token、top_p等参数） */

    private String modelConfig;

    /** 推荐使用的模型 */

    private String recommendedModel;

    /** 是否公开：0-私有，1-公开 */

    private Long isPublic;

    /** 创建者ID */

    private Long creatorId;

    /** 使用次数 */

    private Long usageCount;

    /** 平均评分（1-5分） */

    private BigDecimal rating;

    /** 评分次数 */

    private Long ratingCount;

    /** 模板描述 */

    private String description;

    /** 标签（逗号分隔） */

    private String tags;

    /** 模板变量定义 */

    private String variables;

    /** 示例输入 */

    private String exampleInput;

    /** 示例输出 */

    private String exampleOutput;

    /** 是否启用：0-禁用，1-启用 */

    private Long isActive;

    /** 排序顺序 */

    private Long sortOrder;


    private Date createTime;

    private Date updateTime;


}

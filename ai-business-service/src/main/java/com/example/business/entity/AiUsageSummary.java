package com.example.business.entity;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

import com.example.common.entity.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * AI使用汇总统计对象 ai_usage_summary
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Accessors(chain=true)
public class AiUsageSummary extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 用户ID */

    private Long userId;

    /** AI模型名称 */

    private String model;

    /** 汇总类型：daily-日汇总，monthly-月汇总，yearly-年汇总 */

    private String summaryType;

    /** 汇总日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")

    private Date summaryDate;

    /** 总请求数 */

    private Long totalRequests;

    /** 成功请求数 */

    private Long successfulRequests;

    /** 失败请求数 */

    private Long failedRequests;

    /** 总Token数量 */

    private Long totalTokens;

    /** 输入Token总数 */

    private Long promptTokens;

    /** 输出Token总数 */

    private Long completionTokens;

    /** 总费用（美元） */

    private BigDecimal totalCost;

    /** 平均响应时间（毫秒） */

    private Long avgResponseTime;


}

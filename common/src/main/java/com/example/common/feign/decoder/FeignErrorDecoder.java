package com.example.common.feign.decoder;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONException;
import com.example.common.exception.RemoteServiceException;
import com.example.common.exception.UserFriendlyException;
import com.example.common.result.Result;
import feign.Response;
import feign.codec.ErrorDecoder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;

import java.io.IOException;
import java.nio.charset.StandardCharsets;

/**
 * Feign错误解码器
 * 统一处理微服务间调用的错误响应，将远程服务的错误转换为本地异常
 * 
 * <AUTHOR>
 * @date 2025-07-31
 */
@Slf4j
public class FeignErrorDecoder implements ErrorDecoder {

    private final ErrorDecoder defaultErrorDecoder = new Default();

    @Override
    public Exception decode(String methodKey, Response response) {
        try {
            // 获取响应体内容
            String responseBody = getResponseBody(response);
            
            // 获取服务名称（从methodKey中提取）
            String serviceName = extractServiceName(methodKey);
            
            // 记录详细的错误日志
            log.error("Feign调用失败 - 服务: {}, 方法: {}, 状态码: {}, 响应体: {}", 
                serviceName, methodKey, response.status(), responseBody);
            
            // 根据HTTP状态码和响应内容创建相应的异常
            return createException(serviceName, response.status(), responseBody, methodKey);
            
        } catch (Exception e) {
            log.error("Feign错误解码器处理异常: {}", e.getMessage(), e);
            // 如果解码失败，使用默认的错误解码器
            return defaultErrorDecoder.decode(methodKey, response);
        }
    }

    /**
     * 获取响应体内容
     */
    private String getResponseBody(Response response) {
        try {
            if (response.body() != null) {
                byte[] bodyBytes = response.body().asInputStream().readAllBytes();
                return new String(bodyBytes, StandardCharsets.UTF_8);
            }
        } catch (IOException e) {
            log.warn("读取响应体失败: {}", e.getMessage());
        }
        return "";
    }

    /**
     * 从methodKey中提取服务名称
     */
    private String extractServiceName(String methodKey) {
        try {
            // methodKey格式通常为: ServiceName#methodName(paramTypes)
            if (methodKey.contains("#")) {
                return methodKey.substring(0, methodKey.indexOf("#"));
            }
        } catch (Exception e) {
            log.warn("提取服务名称失败: {}", e.getMessage());
        }
        return "unknown-service";
    }

    /**
     * 根据状态码和响应内容创建相应的异常
     */
    private Exception createException(String serviceName, int status, String responseBody, String methodKey) {
        // 尝试解析响应体为Result对象
        Result<?> result = parseResultFromResponse(responseBody);
        
        if (result != null && result.getCode() != null) {
            // 如果是标准的Result响应，使用其中的错误信息
            return new RemoteServiceException(serviceName, result.getCode(), result.getMessage());
        }
        
        // 根据HTTP状态码处理
        switch (status) {
            case 400:
                return new UserFriendlyException(
                    "请求参数错误，请检查输入信息",
                    String.format("服务[%s]返回400错误: %s", serviceName, responseBody)
                );
                
            case 401:
                return new UserFriendlyException(
                    "认证失败，请重新登录",
                    String.format("服务[%s]返回401错误: %s", serviceName, responseBody)
                );
                
            case 403:
                return new UserFriendlyException(
                    "权限不足，无法访问该资源",
                    String.format("服务[%s]返回403错误: %s", serviceName, responseBody)
                );
                
            case 404:
                return new UserFriendlyException(
                    "请求的资源不存在",
                    String.format("服务[%s]返回404错误: %s", serviceName, responseBody)
                );
                
            case 429:
                return new UserFriendlyException(
                    "请求过于频繁，请稍后重试",
                    String.format("服务[%s]返回429错误: %s", serviceName, responseBody)
                );
                
            case 500:
                return new RemoteServiceException(serviceName, 500, 
                    extractErrorMessage(responseBody, "服务内部错误，请稍后重试"));
                
            case 502:
            case 503:
            case 504:
                return new RemoteServiceException(serviceName, status, 
                    "服务暂时不可用，请稍后重试");
                
            default:
                // 其他状态码
                String errorMessage = extractErrorMessage(responseBody, 
                    String.format("服务调用失败，状态码: %d", status));
                return new RemoteServiceException(serviceName, status, errorMessage);
        }
    }

    /**
     * 尝试从响应体中解析Result对象
     */
    private Result<?> parseResultFromResponse(String responseBody) {
        if (responseBody == null || responseBody.trim().isEmpty()) {
            return null;
        }
        
        try {
            // 检查是否是JSON格式
            if (responseBody.trim().startsWith("{")) {
                return JSON.parseObject(responseBody, Result.class);
            }
        } catch (JSONException e) {
            log.debug("响应体不是有效的JSON格式: {}", responseBody);
        }
        
        return null;
    }

    /**
     * 从响应体中提取错误信息
     */
    private String extractErrorMessage(String responseBody, String defaultMessage) {
        if (responseBody == null || responseBody.trim().isEmpty()) {
            return defaultMessage;
        }
        
        // 如果响应体是纯文本且不太长，直接使用
        if (!responseBody.trim().startsWith("{") && responseBody.length() < 200) {
            return responseBody.trim();
        }
        
        // 尝试从JSON中提取message字段
        try {
            Result<?> result = JSON.parseObject(responseBody, Result.class);
            if (result != null && result.getMessage() != null) {
                return result.getMessage();
            }
        } catch (JSONException e) {
            log.debug("无法解析响应体为JSON: {}", responseBody);
        }
        
        return defaultMessage;
    }
}

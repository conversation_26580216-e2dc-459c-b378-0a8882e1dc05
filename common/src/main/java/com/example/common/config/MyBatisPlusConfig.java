package com.example.common.config;

import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.reflection.MetaObject;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.Date;

/**
 * MyBatis Plus自动填充配置
 */
@Slf4j
@Component
public class MyBatisPlusConfig implements MetaObjectHandler {

    @Override
    public void insertFill(MetaObject metaObject) {
        log.info("开始插入填充...");
        this.strictInsertFill(metaObject, "createTime", Date.class, new Date());
        this.strictInsertFill(metaObject, "updateTime", Date.class, new Date());
//        this.strictInsertFill(metaObject, "deleted", Integer.class, 0);

        // 这里可以从当前登录用户获取用户ID
        // Long userId = getCurrentUserId();
        // this.strictInsertFill(metaObject, "createBy", Long.class, userId);
        // this.strictInsertFill(metaObject, "updateBy", Long.class, userId);
    }

    @Override
    public void updateFill(MetaObject metaObject) {
        log.info("开始更新填充...");
        this.strictUpdateFill(metaObject, "updateTime", Date.class, new Date());

        // 这里可以从当前登录用户获取用户ID
        // Long userId = getCurrentUserId();
        // this.strictUpdateFill(metaObject, "updateBy", Long.class, userId);
    }
}

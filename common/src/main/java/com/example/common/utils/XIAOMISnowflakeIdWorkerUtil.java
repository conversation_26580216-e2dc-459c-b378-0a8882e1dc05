package com.example.common.utils;

import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * <AUTHOR>
 * Create by 2025/8/5 15:37
 * desc 订单号生成
 */
@Component
public class XIAOMISnowflakeIdWorkerUtil {
    @Resource
    private GenericRedisUtil redisService;
    /**
     *
     * @param sourceType 业务编码
     * @return
     */
    public  String nextId(String sourceType) {
        String orderNo = null;
        try {
            //获取当前时间
            Date currentTime = new Date();
            //格式化当前时间为【年的后2位+月+日】
            String originDateStr = new SimpleDateFormat("yyMMdd").format(currentTime);
            //计算当前时间走过的秒
            Date startTime = new SimpleDateFormat("yyyyMMdd").parse(new SimpleDateFormat("yyyyMMdd").format(currentTime));

            long differSecond = (currentTime.getTime() - startTime.getTime()) / 1000;
            //获取【年的后2位+月+日+秒】，秒的长度不足补充0
            String yyMMddSecond = originDateStr + StringUtils.leftPad(String.valueOf(differSecond), 5, '0');

            //获取【业务编码】【年的后2位+月+日+秒】，作为自增key；
            String prefixOrder = sourceType + "" + yyMMddSecond;
            //通过key，采用redis自增函数，实现单秒自增；不同的key，从0开始自增，同时设置60秒过期
            Long incrId=redisService.setIncrement(prefixOrder,1L);
            redisService.expire(prefixOrder,60L);//设置过期时间
            //生成订单编号
            orderNo = prefixOrder + StringUtils.leftPad(String.valueOf(incrId), 4, '0');
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
        return orderNo;
    }
}

package com.example.common.exception;

import com.example.common.result.Result;
import feign.FeignException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.BindException;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import jakarta.validation.ConstraintViolation;
import jakarta.validation.ConstraintViolationException;
import java.util.stream.Collectors;

/**
 * 全局异常处理器
 */
@Slf4j
@RestControllerAdvice
public class GlobalExceptionHandler {

    /**
     * 处理业务异常
     */
    @ExceptionHandler(BusinessException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public Result<Void> handleBusinessException(BusinessException e) {
        log.error("业务异常: {}", e.getMessage());
        return Result.error(e.getCode(), e.getMessage());
    }

    /**
     * 处理支付异常
     */
    @ExceptionHandler(PaymentException.class)
    public ResponseEntity<Result<Void>> handlePaymentException(PaymentException e) {
        // 记录详细的支付异常信息
        log.error("支付异常 - 支付类型: {}, 错误类型: {}, 错误码: {}, 消息: {}",
                 e.getPaymentType(), e.getErrorType(), e.getCode(), e.getMessage(), e);

        // 根据错误类型确定HTTP状态码
        HttpStatus status = HttpStatus.BAD_REQUEST;
        if ("NETWORK_ERROR".equals(e.getErrorType()) || "PAYMENT_FAILED".equals(e.getErrorType())) {
            status = HttpStatus.SERVICE_UNAVAILABLE;
        } else if ("UNSUPPORTED_TYPE".equals(e.getErrorType())) {
            status = HttpStatus.BAD_REQUEST;
        }

        return ResponseEntity.status(status)
                .body(Result.error(e.getCode(), e.getMessage()));
    }

    /**
     * 处理参数校验异常
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public Result<Void> handleValidationException(MethodArgumentNotValidException e) {
        String message = e.getBindingResult().getFieldErrors().stream()
                .map(FieldError::getDefaultMessage)
                .collect(Collectors.joining(", "));
        log.error("参数校验异常: {}", message);
        return Result.error(400, message);
    }

    /**
     * 处理绑定异常
     */
    @ExceptionHandler(BindException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public Result<Void> handleBindException(BindException e) {
        String message = e.getFieldErrors().stream()
                .map(FieldError::getDefaultMessage)
                .collect(Collectors.joining(", "));
        log.error("绑定异常: {}", message);
        return Result.error(400, message);
    }

    /**
     * 处理约束违反异常
     */
    @ExceptionHandler(ConstraintViolationException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public Result<Void> handleConstraintViolationException(ConstraintViolationException e) {
        String message = e.getConstraintViolations().stream()
                .map(ConstraintViolation::getMessage)
                .collect(Collectors.joining(", "));
        log.error("约束违反异常: {}", message);
        return Result.error(400, message);
    }

    /**
     * 处理Feign调用异常 - 返回纯文本
     * 这个处理器作为兜底，处理未被其他异常处理器捕获的FeignException
     */
    @ExceptionHandler(FeignException.class)
    public ResponseEntity<String> handleFeignException(FeignException e) {
        log.error("Feign调用异常: 状态码={}, 响应体={}", e.status(), e.contentUTF8(), e);

        // 尝试提取响应体中的错误信息
        String responseBody = e.contentUTF8();
        String errorMessage = "服务暂时不可用，请稍后重试";

        if (responseBody != null && !responseBody.trim().isEmpty()) {
            // 如果响应体不是JSON格式，直接使用
            if (!responseBody.trim().startsWith("{") && !responseBody.trim().startsWith("[")) {
                errorMessage = responseBody.trim();
            } else {
                errorMessage = "服务调用失败: " + responseBody;
            }
        }

        return ResponseEntity.status(HttpStatus.SERVICE_UNAVAILABLE)
                .contentType(MediaType.TEXT_PLAIN)
                .body(errorMessage);
    }

    /**
     * 处理运行时异常 - 返回纯文本
     */
    @ExceptionHandler(RuntimeException.class)
    public ResponseEntity<String> handleRuntimeExceptionText(RuntimeException e) {
        log.error("运行时异常: ", e);
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .contentType(MediaType.TEXT_PLAIN)
                .body("系统繁忙，请稍后重试");
    }

    /**
     * 处理远程服务调用异常
     */
    @ExceptionHandler(RemoteServiceException.class)
    public ResponseEntity<String> handleRemoteServiceException(RemoteServiceException e) {
        // 开发者日志记录详细信息
        log.error("远程服务调用异常 - 服务: {}, 错误码: {}, 原始消息: {}",
                 e.getServiceName(), e.getCode(), e.getOriginalMessage(), e);

        // 根据错误码判断HTTP状态码
        HttpStatus status = HttpStatus.SERVICE_UNAVAILABLE;
        if (e.getCode() != null) {
            if (e.getCode() >= 400 && e.getCode() < 500) {
                status = HttpStatus.BAD_REQUEST;
            } else if (e.getCode() >= 500) {
                status = HttpStatus.INTERNAL_SERVER_ERROR;
            }
        }

        // 返回纯文本响应给用户，包含服务端的具体错误信息
        return ResponseEntity.status(status)
                .contentType(MediaType.TEXT_PLAIN)
                .body(e.getOriginalMessage());
    }

    /**
     * 处理用户友好异常 - 返回纯文本
     */
    @ExceptionHandler(UserFriendlyException.class)
    public ResponseEntity<String> handleUserFriendlyException(UserFriendlyException e) {
        // 开发者日志记录详细信息
        log.error("用户友好异常 - 开发者信息: {}", e.getDeveloperMessage());

        // 根据异常信息判断HTTP状态码
        HttpStatus status = HttpStatus.BAD_REQUEST;
        if (e.getDeveloperMessage() != null && e.getDeveloperMessage().contains("服务降级")) {
            status = HttpStatus.SERVICE_UNAVAILABLE;
        } else if (e.getDeveloperMessage() != null && e.getDeveloperMessage().contains("Feign")) {
            status = HttpStatus.SERVICE_UNAVAILABLE;
        }

        // 返回纯文本响应给用户
        return ResponseEntity.status(status)
                .contentType(MediaType.TEXT_PLAIN)
                .body(e.getUserMessage());
    }

    /**
     * 处理其他异常
     */
    @ExceptionHandler(Exception.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public Result<Void> handleException(Exception e) {
        log.error("系统异常: ", e);
        return Result.error(500, "系统内部错误");
    }


}

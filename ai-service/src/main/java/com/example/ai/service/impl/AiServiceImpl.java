package com.example.ai.service.impl;

import com.alibaba.dashscope.aigc.generation.Generation;
import com.alibaba.dashscope.aigc.generation.GenerationParam;
import com.alibaba.dashscope.aigc.generation.GenerationResult;
import com.alibaba.dashscope.common.Message;
import com.alibaba.dashscope.common.Role;
import com.alibaba.dashscope.common.ResponseFormat;
import com.alibaba.dashscope.exception.ApiException;
import com.alibaba.dashscope.exception.InputRequiredException;
import com.alibaba.dashscope.exception.NoApiKeyException;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.example.ai.dto.ChatInfoRequest;
import com.example.ai.dto.ChatInfoResponse;
import com.example.ai.feign.BusinessFeign;
import com.example.ai.service.AiService;
import com.example.ai.service.ChatInfoService;
import com.example.ai.threadpool.manager.ThreadPoolManager;
import com.example.ai.util.JsonUtils;
import com.example.common.entity.dto.AiChatMessageDto;
import com.example.common.entity.vo.AiChatMessageVo;
import com.example.common.entity.vo.AiChatSessionVo;
import com.openai.client.OpenAIClient;
import com.openai.client.okhttp.OpenAIOkHttpClient;
import com.openai.models.chat.completions.ChatCompletion;
import com.openai.models.chat.completions.ChatCompletionCreateParams;
import io.reactivex.Flowable;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * AI服务实现类
 * 提供单轮对话、多轮对话和流式对话功能
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AiServiceImpl implements AiService {


    private final ChatInfoService chatInfoService;
    private final ThreadPoolManager threadPoolManager;
    private final BusinessFeign businessFeign;

    // 配置属性注入
    @Value("${ai.dashscope.api-key:sk-fbbbf1f6917e4c0cb7ef2d30cd647547}")
    private String apiKey;

    @Value("${ai.dashscope.base-url:https://dashscope.aliyuncs.com/compatible-mode/v1}")
    private String baseUrl;

    @Value("${ai.dashscope.model:qwen-plus}")
    private String model;

    @Value("${ai.stream.timeout:1800000}")  // 30分钟默认超时
    private Long streamTimeout;

    // Generation实例复用
    private Generation generation;

    /**
     * 初始化方法
     */
    @PostConstruct
    public void init() {
        // 初始化Generation实例
        this.generation = new Generation();

        log.info("AI服务初始化完成 - 模型: {}, 超时时间: {}ms",
                model, streamTimeout);
    }





    /**
     * 获取线程池状态信息（用于监控）
     */
    public Map<String, Object> getThreadPoolStatus() {
        return threadPoolManager.getThreadPoolStatus();
    }


    @Override
    public String singleChatCompletionText(String message, String chatId) {
        OpenAIClient client = OpenAIOkHttpClient.builder()
                .apiKey(apiKey)
                .baseUrl(baseUrl)
                .build();

        ChatCompletionCreateParams params = ChatCompletionCreateParams.builder()
                .addUserMessage(message)
                .model(model)
                .build();
        try {
            ChatCompletion chatCompletion = client.chat().completions().create(params);
            String result = chatCompletion.choices().get(0).message().content().get();


            //持久化保存对话内容
            ChatInfoRequest chatInfoRequest = new ChatInfoRequest()
                    .setChatId(chatId)
                    .setChatInfoTitle(message)
                    .setAiResponse(chatCompletion.toString())
                    .setChatInfoContent(result);

            chatInfoService.saveChat(chatInfoRequest);

            log.info("保存对话成功");

            return result;
        } catch (Exception e) {
            System.err.println("Error occurred: " + e.getMessage());
            e.printStackTrace();
        }
        return "";
    }

    @Override
    public String singleGenerationResultJson(String message) {
        try {
            GenerationResult result = callWithMessage(message);
            System.err.println("输出完成");
            String jsonString = result.getOutput().getChoices().get(0).getMessage().getContent();

            return jsonString;
        } catch (ApiException | NoApiKeyException | InputRequiredException e) {
            // 使用日志框架记录异常信息
            System.err.println("An error occurred while calling the generation service: " + e.getMessage());
        }
        return "";
    }


    /**
     * 单轮对话调用（JSON模式）
     */
    public GenerationResult callWithMessage(String message) throws ApiException, NoApiKeyException, InputRequiredException {
        Message systemMsg = Message.builder()
                .role(Role.SYSTEM.getValue())
                .content(message)
                .build();

        ResponseFormat jsonMode = ResponseFormat.builder().type("json_object").build();
        GenerationParam param = GenerationParam.builder()
                .apiKey(apiKey)
                .model(model)
                .messages(Arrays.asList(systemMsg))
                .resultFormat(GenerationParam.ResultFormat.MESSAGE)
                .responseFormat(jsonMode)
                .build();
        return generation.call(param);
    }


    @Override
    public String multiwheelGenerationResultJson(String message, Integer type,String chatId) {
        try {
            //存储用于多轮对话
            List<Message> messages = new ArrayList<>();
            //初始化历史对话
            messages.add(createMessage(Role.SYSTEM, "You are a helpful assistant."));
            messages.add(createMessage(Role.USER, message));
            //根据chatId获取历史对话
            List<ChatInfoResponse> chatInfoResponseList = chatInfoService.getByChatIdAndType(chatId);
            if(chatInfoResponseList != null && chatInfoResponseList.size() > 0){
                chatInfoResponseList.forEach(e->{
                    //解析历史对话
                    Map<String, Object> messageObject = JsonUtils.parseQwenMessageObject(e.getAiResponse());
                    //将历史对话添加到messages中
                    Message message1 = new Message();
                    message1.setRole(messageObject.get("role").toString());
                    message1.setContent(messageObject.get("content").toString());
                    messages.add(message1);
                });
            }

            GenerationParam param = createGenerationParam(messages, type);
            GenerationResult result = callGenerationWithMessages(param);
            String jsonString = result.getOutput().getChoices().get(0).getMessage().getContent();
            log.info("多轮对话生成完成，对话ID: {}", chatId);

            String resultJson = JSON.toJSONString(result);

            //持久化保存对话内容
            ChatInfoRequest chatInfoRequest = new ChatInfoRequest()
                    .setChatId(chatId)
                    .setChatInfoTitle(message)
                    .setAiResponse(resultJson)
                    .setChatInfoContent(jsonString);
            chatInfoService.saveChat(chatInfoRequest);

            log.info("保存多轮对话成功");
            //将当前生成得到的回复添加到messages中
            messages.add(result.getOutput().getChoices().get(0).getMessage());
            return jsonString;
//            }
        } catch (ApiException | NoApiKeyException | InputRequiredException e) {
            e.printStackTrace();
        }
        return "";
    }


    /**
     * 创建生成参数（支持JSON模式）
     */
    private GenerationParam createGenerationParam(List<Message> messages, Integer type) {
        ResponseFormat jsonMode = (type == 0)
            ? ResponseFormat.builder().type("json_object").build()
            : ResponseFormat.builder().type("text").build();

        return GenerationParam.builder()
                .apiKey(apiKey)
                .model(model)
                .messages(messages)
                .resultFormat(GenerationParam.ResultFormat.MESSAGE)
                .responseFormat(jsonMode)
                .build();
    }

    /**
     * 调用生成接口
     */
    private GenerationResult callGenerationWithMessages(GenerationParam param) throws ApiException, NoApiKeyException, InputRequiredException {
        return generation.call(param);
    }

    /**
     * 创建消息对象
     */
    private Message createMessage(Role role, String content) {
        return Message.builder().role(role.getValue()).content(content).build();
    }




    @Override
    public SseEmitter streamChat(String message, String chatId) {
        // 参数验证
        if (!StringUtils.hasText(message)) {
            throw new IllegalArgumentException("消息内容不能为空");
        }
        if (!StringUtils.hasText(chatId)) {
            throw new IllegalArgumentException("对话ID不能为空");
        }

        //程序执行开始时间
        long start = System.currentTimeMillis();

        // 创建SseEmitter，使用配置的超时时间
        SseEmitter emitter = new SseEmitter(streamTimeout);

        // 连接状态跟踪
        final AtomicBoolean isConnected = new AtomicBoolean(true);

        // 构建对话消息列表
        List<Message> messages = buildConversationHistory(chatId);


        // 添加当前用户消息
        Message userMsg = Message.builder()
                .role(Role.USER.getValue())
                .content(message)
                .build();
        messages.add(userMsg);

        // 设置连接状态监听器
        setupEmitterListeners(emitter, isConnected, chatId);

        // 获取AI流式处理线程池
        ExecutorService streamExecutor = threadPoolManager.getAiStreamThreadPool();
        if (streamExecutor == null || streamExecutor.isShutdown()) {
            log.error("AI流式处理线程池不可用 - 对话ID: {}", chatId);
            emitter.completeWithError(new IllegalStateException("AI流式处理服务不可用，请稍后重试"));
            return emitter;
        }

        // 使用线程池异步处理流式响应
        try {
            streamExecutor.submit(() -> {
            try {
                log.info("开始流式对话 - 消息: {}, 对话ID: {}", message, chatId);

                // 构建生成参数
                GenerationParam param = buildGenerationParam(messages);

                // 获取流式响应
                Flowable<GenerationResult> result = generation.streamCall(param);

                // 处理流式响应
                String fullContent = processStreamResponse(result, emitter, isConnected, chatId);


                //程序执行结束时间
                long end = System.currentTimeMillis();
                //总耗时
                long duration = end - start;
                log.info("流式输出总耗时:{}",duration);
                // 保存对话并完成流式传输
                completeStreamChat(emitter, isConnected, chatId, message, fullContent,duration);

                } catch (Exception e) {
                    handleStreamError(emitter, isConnected, chatId, e);
                }
            });
        } catch (RejectedExecutionException e) {
            log.error("线程池队列已满，拒绝处理流式请求 - 对话ID: {}", chatId);
            emitter.completeWithError(new IllegalStateException("服务繁忙，请稍后重试"));
        } catch (Exception e) {
            log.error("提交流式任务失败 - 对话ID: {}", chatId, e);
            emitter.completeWithError(e);
        }

        return emitter;
    }

    /**
     * 构建对话历史记录
     */
    private List<Message> buildConversationHistory(String chatId) {
        List<Message> messages = new ArrayList<>();
        try {
            //根据chatId获取历史对话
            String result = businessFeign.getChatMessageAiInfo(chatId);
            JSONArray jsonArray = JSON.parseArray(result);
            List<AiChatMessageVo> chatHistory = jsonArray.toJavaList(AiChatMessageVo.class);

            log.info("加载历史对话成功，结果急为：{}",chatHistory.size());
            if (chatHistory != null && !chatHistory.isEmpty()) {
                for (AiChatMessageVo chat : chatHistory) {
                    // 添加用户消息
                    if (StringUtils.hasText(chat.getContent())) {
                        Message userMsg = Message.builder()
                                .role(Role.USER.getValue())
                                .content(chat.getContent())
                                .build();
                        messages.add(userMsg);
                    }

                    // 添加AI回复
                    if (StringUtils.hasText(chat.getRawContent())) {
                        Message aiMsg = Message.builder()
                                .role(Role.ASSISTANT.getValue())
                                .content(chat.getContent())
                                .build();
                        messages.add(aiMsg);
                    }
                }
                log.debug("加载历史对话成功，共{}条消息", messages.size());
            }
        } catch (Exception e) {
            log.warn("加载历史对话失败，对话ID: {}, 错误: {}", chatId, e.getMessage());
        }

        return messages;
    }

    /**
     * 设置SSE连接监听器
     */
    private void setupEmitterListeners(SseEmitter emitter, AtomicBoolean isConnected, String chatId) {
        emitter.onCompletion(() -> {
            isConnected.set(false);
            log.info("SSE连接正常完成 - 对话ID: {}", chatId);
        });

        emitter.onTimeout(() -> {
            isConnected.set(false);
            log.warn("SSE连接超时 - 对话ID: {}", chatId);
        });

        emitter.onError(throwable -> {
            isConnected.set(false);
            log.warn("SSE连接异常断开 - 对话ID: {}, 原因: {}", chatId, throwable.getMessage());
        });
    }

    /**
     * 构建生成参数
     */
    private GenerationParam buildGenerationParam(List<Message> messages) {
        return GenerationParam.builder()
                .apiKey(apiKey)
                .model(model)
                .messages(messages)
                .resultFormat(GenerationParam.ResultFormat.MESSAGE)
                .incrementalOutput(true)  // 启用增量输出
                .build();
    }

    /**
     * 处理流式响应
     */
    private String processStreamResponse(Flowable<GenerationResult> result, SseEmitter emitter,
                                       AtomicBoolean isConnected, String chatId) {
        StringBuilder fullContent = new StringBuilder();

        try {
            result.blockingForEach(generationResult -> {
                if (!isConnected.get()) {
                    log.debug("客户端已断开连接，停止处理 - 对话ID: {}", chatId);
                    return;
                }

                try {
                    String content = generationResult.getOutput().getChoices().get(0).getMessage().getContent();
//                    log.info("流式输出的内容: {}", content);
                    fullContent.append(content);

                    if (isConnected.get()) {
                        sendStreamData(emitter, content, isConnected, chatId);
                    }
                } catch (Exception e) {
                    log.error("处理流式数据失败 - 对话ID: {}", chatId, e);
                    isConnected.set(false);
                    throw e;
                }
            });
        } catch (Exception e) {
            log.error("流式响应处理异常 - 对话ID: {}", chatId, e);
            throw new RuntimeException("流式响应处理失败", e);
        }

        return fullContent.toString();
    }

    /**
     * 发送流式数据
     */
    private void sendStreamData(SseEmitter emitter, String content, AtomicBoolean isConnected, String chatId) {
        try {
            emitter.send(SseEmitter.event()
                    .name("message")
                    .data(content));
            log.debug("发送流式数据成功 - 对话ID: {}", chatId);
        } catch (IllegalStateException e) {
            if (e.getMessage() != null && e.getMessage().contains("ResponseBodyEmitter has already completed")) {
                isConnected.set(false);
                log.debug("连接已关闭，停止发送数据 - 对话ID: {}", chatId);
            } else {
                throw e;
            }
        } catch (Exception e) {
            log.error("发送流式数据失败 - 对话ID: {}", chatId, e);
            isConnected.set(false);
            throw new RuntimeException(e);
        }
    }

    /**
     * 完成流式对话
     */
    private void completeStreamChat(SseEmitter emitter, AtomicBoolean isConnected,
                                  String chatId, String message, String fullContent,long duration) {
        if (!isConnected.get()) {
            log.debug("连接已断开，跳过保存和完成操作 - 对话ID: {}", chatId);
            return;
        }

        try {
            // 保存对话到数据库
            saveChatToDatabase(chatId, message, fullContent,duration);

            // 发送完成信号
            sendCompletionSignal(emitter, isConnected, chatId);

        } catch (Exception e) {
            log.error("完成流式对话失败 - 对话ID: {}", chatId, e);
            handleStreamError(emitter, isConnected, chatId, e);
        }
    }

    /**
     * 保存对话到数据库
     */
    private void saveChatToDatabase(String chatId, String message, String fullContent,long duration) {
        try {
//            ChatInfoRequest chatInfoRequest = new ChatInfoRequest()
//                    .setChatId(chatId)
//                    .setChatInfoTitle(message)
//                    .setAiResponse(fullContent)
//                    .setChatInfoContent(fullContent);
//
//            chatInfoService.saveChat(chatInfoRequest);
            AiChatMessageDto build = AiChatMessageDto.builder()
                    .sessionId(chatId)
                    .content(message)
                    .rawContent(fullContent)
                    .responseTime(duration)
                    .build();
            businessFeign.saveChatMessage(build);
//            log.info("保存对话成功 - 对话ID: {}", chatId);
        } catch (Exception e) {
            log.error("保存对话失败 - 对话ID: {}", chatId, e);
            throw e;
        }
    }

    /**
     * 发送完成信号
     */
    private void sendCompletionSignal(SseEmitter emitter, AtomicBoolean isConnected, String chatId) {
        try {
            emitter.send(SseEmitter.event()
                    .name("complete")
                    .data("对话完成"));

            emitter.complete();
            log.info("流式对话完成 - 对话ID: {}", chatId);
        } catch (IllegalStateException e) {
            if (e.getMessage() != null && e.getMessage().contains("ResponseBodyEmitter has already completed")) {
                log.debug("连接已关闭，无需发送完成信号 - 对话ID: {}", chatId);
            } else {
                throw e;
            }
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 处理流式错误
     */
    private void handleStreamError(SseEmitter emitter, AtomicBoolean isConnected, String chatId, Exception e) {
        log.error("流式对话异常 - 对话ID: {}", chatId, e);

        if (!isConnected.get()) {
            return;
        }

        try {
            emitter.send(SseEmitter.event()
                    .name("error")
                    .data("对话处理失败: " + e.getMessage()));
            emitter.completeWithError(e);
        } catch (IllegalStateException sendError) {
            if (sendError.getMessage() != null && sendError.getMessage().contains("ResponseBodyEmitter has already completed")) {
                log.debug("连接已关闭，无法发送错误信息 - 对话ID: {}", chatId);
            } else {
                log.error("发送错误信息失败 - 对话ID: {}", chatId, sendError);
            }
        } catch (Exception sendError) {
            log.error("发送错误信息失败 - 对话ID: {}", chatId, sendError);
        }
    }
}

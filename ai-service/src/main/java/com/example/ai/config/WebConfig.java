package com.example.ai.config;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * Web配置类
 * 配置静态资源访问路径，使上传的文件可以通过HTTP访问
 * 
 * <AUTHOR> Service
 * @date 2025-07-24
 */
@Slf4j
@Configuration
@RequiredArgsConstructor
public class WebConfig implements WebMvcConfigurer {

    private final FileUploadConfig fileUploadConfig;

    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        // 配置文件访问路径
        String uploadPath = fileUploadConfig.getPath();

        // 创建uploads目录（如果不存在）
        createUploadDirectoryIfNotExists(uploadPath);

        // 确保路径以 file: 开头，以 / 结尾
        if (!uploadPath.startsWith("file:")) {
            if (uploadPath.startsWith("./")) {
                // 相对路径转换为绝对路径
                uploadPath = "file:" + System.getProperty("user.dir") + "/" + uploadPath.substring(2);
            } else if (!uploadPath.startsWith("/")) {
                // 相对路径转换为绝对路径
                uploadPath = "file:" + System.getProperty("user.dir") + "/" + uploadPath;
            } else {
                // 绝对路径
                uploadPath = "file:" + uploadPath;
            }
        }

        if (!uploadPath.endsWith("/")) {
            uploadPath += "/";
        }

        // 添加静态资源处理器
        registry.addResourceHandler("/files/**")
                .addResourceLocations(uploadPath)
                .setCachePeriod(3600); // 缓存1小时

        log.info("配置文件访问路径: /files/** -> {}", uploadPath);

        // 添加favicon.ico处理，避免404错误
        registry.addResourceHandler("/favicon.ico")
                .addResourceLocations("classpath:/static/")
                .setCachePeriod(86400); // 缓存1天
    }

    /**
     * 创建上传目录（如果不存在）
     */
    private void createUploadDirectoryIfNotExists(String uploadPath) {
        try {
            java.nio.file.Path path;
            if (uploadPath.startsWith("./")) {
                path = java.nio.file.Paths.get(System.getProperty("user.dir"), uploadPath.substring(2));
            } else if (!uploadPath.startsWith("/")) {
                path = java.nio.file.Paths.get(System.getProperty("user.dir"), uploadPath);
            } else {
                path = java.nio.file.Paths.get(uploadPath);
            }

            if (!java.nio.file.Files.exists(path)) {
                java.nio.file.Files.createDirectories(path);
                log.info("创建上传目录: {}", path.toAbsolutePath());
            } else {
                log.info("上传目录已存在: {}", path.toAbsolutePath());
            }
        } catch (Exception e) {
            log.error("创建上传目录失败: {}", e.getMessage(), e);
        }
    }
}

package com.example.payment.config;

import com.example.payment.enums.OrderTypeEnum;
import com.example.payment.strategy.TransactionProcessorStrategy;
import com.example.payment.strategy.impl.RechargeTransactionProcessor;
import com.example.payment.strategy.impl.VipTransactionProcessor;
import com.example.payment.strategy.impl.ConsumeTransactionProcessor;
import com.example.payment.strategy.impl.RefundTransactionProcessor;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * 策略配置测试类
 */
@SpringBootTest
public class TransactionStrategyConfigTest {

    @Test
    public void testStrategyMapping() {
        // 创建策略实例
        List<TransactionProcessorStrategy> strategies = Arrays.asList(
                new RechargeTransactionProcessor(),
                new VipTransactionProcessor(),
                new ConsumeTransactionProcessor(),
                new RefundTransactionProcessor()
        );

        // 创建配置实例
        TransactionStrategyConfig config = new TransactionStrategyConfig();
        
        // 测试策略注册
        Map<String, TransactionProcessorStrategy> processors = config.transactionProcessors(strategies);
        
        System.out.println("=== 测试结果 ===");
        System.out.println("Map大小: " + processors.size());
        
        // 测试每个枚举值
        for (OrderTypeEnum orderType : OrderTypeEnum.values()) {
            String code = orderType.getCode();
            TransactionProcessorStrategy processor = processors.get(code);
            System.out.println("枚举: " + orderType.name() + 
                             ", code: '" + code + "'" +
                             ", 找到处理器: " + (processor != null ? processor.getClass().getSimpleName() : "null"));
        }
        
        // 测试具体的recharge
        String rechargeCode = OrderTypeEnum.RECHARGE.getCode();
        TransactionProcessorStrategy rechargeProcessor = processors.get(rechargeCode);
        System.out.println("直接测试recharge: '" + rechargeCode + "' -> " + 
                          (rechargeProcessor != null ? rechargeProcessor.getClass().getSimpleName() : "null"));
    }
}

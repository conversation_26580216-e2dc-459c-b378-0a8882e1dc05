package com.example.payment.strategy.impl;

import com.example.payment.entity.Order;
import com.example.payment.entity.Transaction;
import com.example.payment.enums.OrderTypeEnum;
import com.example.payment.strategy.TransactionProcessorStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * VIP购买交易处理策略
 */
@Slf4j
@Component
public class VipTransactionProcessor implements TransactionProcessorStrategy {

    @Override
    public Transaction processTransaction(Order order) {
        log.info("处理VIP购买交易，订单ID: {}, 金额: {}", order.getId(), order.getAmount());
        
        // VIP购买特有的业务逻辑
        // 1. VIP购买不影响余额，但需要记录交易
        Long currentBalance = getCurrentUserBalance(order.getUserId());
        
        // 2. 构建交易记录
        return Transaction.builder()
                .userId(order.getUserId())
                .orderId(order.getId())
                .type(order.getOrderType())
                .amount(order.getAmount().longValue())
                .balance(currentBalance) // VIP购买不改变余额
                .description("购买VIP会员")
                .paymentMethodId(order.getPaymentMethodId())
                .status("pending")
                .build();
    }

    @Override
    public String getSupportedOrderType() {
        return OrderTypeEnum.VIP.getCode();
    }

    /**
     * 获取用户当前余额
     * TODO: 实现用户余额查询逻辑
     */
    private Long getCurrentUserBalance(Long userId) {
        // 这里应该调用用户服务获取当前余额
        // 暂时返回0，实际项目中需要实现
        return 0L;
    }
}

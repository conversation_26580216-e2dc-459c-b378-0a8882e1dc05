package com.example.payment.strategy;

import com.example.payment.entity.Order;
import com.example.payment.entity.Transaction;

/**
 * 交易处理策略接口
 * 用于处理不同类型的订单交易逻辑
 */
public interface TransactionProcessorStrategy {

    /**
     * 处理交易
     * @param order 订单信息
     * @return 交易记录
     */
    Transaction processTransaction(Order order);

    /**
     * 获取支持的订单类型
     * @return 订单类型代码
     */
    String getSupportedOrderType();
}

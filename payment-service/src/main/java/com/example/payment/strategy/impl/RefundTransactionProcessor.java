package com.example.payment.strategy.impl;

import com.example.payment.entity.Order;
import com.example.payment.entity.Transaction;
import com.example.payment.enums.OrderTypeEnum;
import com.example.payment.strategy.TransactionProcessorStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 退款交易处理策略
 */
@Slf4j
@Component
public class RefundTransactionProcessor implements TransactionProcessorStrategy {

    @Override
    public Transaction processTransaction(Order order) {
        log.info("处理退款交易，订单ID: {}, 金额: {}", order.getId(), order.getAmount());
        
        // 退款特有的业务逻辑
        // 1. 计算退款后的余额
        Long currentBalance = getCurrentUserBalance(order.getUserId());
        Long newBalance = currentBalance + order.getAmount().longValue();
        
        // 2. 构建交易记录
        return Transaction.builder()
                .userId(order.getUserId())
                .orderId(order.getId())
                .type(order.getOrderType())
                .amount(order.getAmount().longValue()) // 退款金额为正数
                .balance(newBalance)
                .description("订单退款")
                .paymentMethodId(order.getPaymentMethodId())
                .status("pending")
                .build();
    }

    @Override
    public String getSupportedOrderType() {
        return OrderTypeEnum.REFUND.getCode();
    }

    /**
     * 获取用户当前余额
     * TODO: 实现用户余额查询逻辑
     */
    private Long getCurrentUserBalance(Long userId) {
        // 这里应该调用用户服务获取当前余额
        // 暂时返回0，实际项目中需要实现
        return 0L;
    }
}

package com.example.payment.strategy.impl;

import com.example.payment.entity.Order;
import com.example.payment.entity.Transaction;
import com.example.payment.enums.OrderTypeEnum;
import com.example.payment.strategy.TransactionProcessorStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 消费交易处理策略
 */
@Slf4j
@Component
public class ConsumeTransactionProcessor implements TransactionProcessorStrategy {

    @Override
    public Transaction processTransaction(Order order) {
        log.info("处理消费交易，订单ID: {}, 金额: {}", order.getId(), order.getAmount());
        
        // 消费特有的业务逻辑
        // 1. 计算消费后的余额
        Long currentBalance = getCurrentUserBalance(order.getUserId());
        Long newBalance = currentBalance - order.getAmount().longValue();
        
        // 2. 检查余额是否足够
        if (newBalance < 0) {
            throw new IllegalStateException("用户余额不足，无法完成消费");
        }
        
        // 3. 构建交易记录
        return Transaction.builder()
                .userId(order.getUserId())
                .orderId(order.getId())
                .type(order.getOrderType())
                .amount(-order.getAmount().longValue()) // 消费金额为负数
                .balance(newBalance)
                .description("账户消费")
                .paymentMethodId(order.getPaymentMethodId())
                .status("pending")
                .build();
    }

    @Override
    public String getSupportedOrderType() {
        return OrderTypeEnum.CONSUME.getCode();
    }

    /**
     * 获取用户当前余额
     * TODO: 实现用户余额查询逻辑
     */
    private Long getCurrentUserBalance(Long userId) {
        // 这里应该调用用户服务获取当前余额
        // 暂时返回10000L，实际项目中需要实现
        return 10000L;
    }
}

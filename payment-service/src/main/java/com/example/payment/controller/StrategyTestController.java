package com.example.payment.controller;

import com.example.common.entity.Result;
import com.example.payment.entity.Order;
import com.example.payment.enums.OrderTypeEnum;
import com.example.payment.service.TransactionService;
import com.example.payment.strategy.TransactionProcessorStrategy;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.util.Map;
import java.util.UUID;

/**
 * 策略测试控制器
 */
@Slf4j
@RestController
@RequestMapping("/strategy-test")
@RequiredArgsConstructor
public class StrategyTestController {

    private final Map<String, TransactionProcessorStrategy> transactionProcessors;
    private final TransactionService transactionService;

    /**
     * 测试策略Map
     */
    @GetMapping("/map")
    public Result<Object> testStrategyMap() {
        log.info("=== 策略Map测试 ===");
        log.info("Map大小: {}", transactionProcessors.size());
        log.info("Map keys: {}", transactionProcessors.keySet());
        
        // 测试每个枚举值
        for (OrderTypeEnum orderType : OrderTypeEnum.values()) {
            String code = orderType.getCode();
            TransactionProcessorStrategy processor = transactionProcessors.get(code);
            log.info("枚举: {}, code: '{}', 处理器: {}", 
                    orderType.name(), code, 
                    processor != null ? processor.getClass().getSimpleName() : "null");
        }
        
        return Result.success(transactionProcessors.keySet());
    }

    /**
     * 测试具体的策略获取
     */
    @GetMapping("/get/{orderType}")
    public Result<String> testGetStrategy(@PathVariable String orderType) {
        log.info("测试获取策略: '{}'", orderType);
        
        TransactionProcessorStrategy processor = transactionProcessors.get(orderType);
        String result = processor != null ? processor.getClass().getSimpleName() : "未找到处理器";
        
        log.info("结果: {}", result);
        return Result.success(result);
    }

    /**
     * 测试创建交易
     */
    @GetMapping("/transaction/{orderType}")
    public Result<String> testCreateTransaction(@PathVariable String orderType) {
        try {
            // 创建测试订单
            Order testOrder = Order.builder()
                    .id(UUID.randomUUID().toString())
                    .userId(1L)
                    .orderType(orderType)
                    .amount(new BigDecimal("100.00"))
                    .paymentMethodId("test")
                    .status("pending")
                    .build();
            
            // 调用交易服务
            transactionService.createTransaction(testOrder);
            
            return Result.success("交易创建成功");
        } catch (Exception e) {
            log.error("交易创建失败", e);
            return Result.error(500, "交易创建失败: " + e.getMessage());
        }
    }
}

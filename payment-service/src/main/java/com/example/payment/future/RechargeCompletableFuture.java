package com.example.payment.future;

import com.example.common.config.RechargeThreadConfig;
import com.example.common.utils.XIAOMISnowflakeIdWorkerUtil;
import com.example.payment.entity.vo.PaymentMethodVo;
import com.example.payment.entity.vo.QuickRechargeOptionVo;
import com.example.payment.service.PaymentMethodService;
import com.example.payment.service.impl.QuickRechargeOptionServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR>
 * Create by 2025/7/28 16:18
 * desc 客户端充值相关异步
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class RechargeCompletableFuture {

    private final QuickRechargeOptionServiceImpl quickRechargeOptionService;

    private final PaymentMethodService paymentMethodService;

    private final XIAOMISnowflakeIdWorkerUtil xiaomiSnowflakeIdWorkerUtil;

    //线程配置类
    private final RechargeThreadConfig rechargeThreadConfig;

    /**
     * 快速充值选项列表
     * @return
     */
    public CompletableFuture<List<QuickRechargeOptionVo>> queryRechargeList() {
        CompletableFuture<List<QuickRechargeOptionVo>> listCompletableFuture = CompletableFuture.supplyAsync(() -> {
            log.info("异步查询充值列表,当前线程名称：{}",Thread.currentThread().getName());
            List<QuickRechargeOptionVo> quickRechargeOptionVoList = quickRechargeOptionService.getList();
            return quickRechargeOptionVoList;
        },rechargeThreadConfig.threadPoolTaskExecutor());
        return listCompletableFuture;
    }

    /**
     * 支付方式列表
     * @return
     */
    public CompletableFuture<List<PaymentMethodVo>> queryPaymentMethodList() {
        CompletableFuture<List<PaymentMethodVo>> listCompletableFuture = CompletableFuture.supplyAsync(() -> {
            log.info("异步查询支付方式列表,当前线程名称：{}",Thread.currentThread().getName());
            List<PaymentMethodVo> paymentMethodList = paymentMethodService.paymentMethodList();
            return paymentMethodList;
        },rechargeThreadConfig.threadPoolTaskExecutor());
        return listCompletableFuture;
    }

    /**
     * 初始化订单号
     * @return
     */
    public CompletableFuture<String> queryOrderNum() {
        CompletableFuture<String> uCompletableFuture = CompletableFuture.supplyAsync(() -> {
            log.info("初始化订单号，防止重复提交,当前线程名称：{}", Thread.currentThread().getName());
            String orderNum = xiaomiSnowflakeIdWorkerUtil.nextId("1");
            return orderNum;
        }, rechargeThreadConfig.threadPoolTaskExecutor());
        return uCompletableFuture;
    }
}

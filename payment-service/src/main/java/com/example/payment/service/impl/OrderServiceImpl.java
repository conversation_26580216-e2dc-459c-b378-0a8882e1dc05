package com.example.payment.service.impl;

import com.alipay.api.internal.util.StringUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.example.common.entity.vo.OrderVo;
import com.example.common.utils.GenericRedisUtil;
import com.example.payment.client.PaySimpleFactory;
import com.example.payment.entity.Order;
import com.example.payment.entity.dto.OrderDto;
import com.example.payment.mapper.OrderMapper;
import com.example.payment.service.OrderService;
import com.example.payment.util.PaymentConverter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 订单服务实现类
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class OrderServiceImpl extends ServiceImpl<OrderMapper, Order> implements OrderService {

    private final OrderMapper orderMapper;

    private final GenericRedisUtil genericRedisUtil;

    private final PaySimpleFactory paySimpleFactory;

    private final TransactionServiceImpl transactionService;

    @Override
    @Transactional(rollbackFor =  Exception.class)
    public String createOrder(OrderDto orderDto) {
        //根据订单号查询订单
        Order order = orderMapper.selectByOrderNum(orderDto.getOrderNum());
        if(Objects.nonNull(order)){
            throw new RuntimeException("订单已创建，请勿重复创建");
        }

        //构建订单
         order = Order.builder()
                .userId(1L)
                .orderNum(orderDto.getOrderNum())
                .orderType(orderDto.getOrderType())
                .amount(orderDto.getAmount())
                .paymentMethod(orderDto.getPaymentMethod())
                .build();
        //保存订单
        this.save(order);
        //保存交易记录
        transactionService.createTransaction(order);

        //加入redis生成订单过期倒计时
        //获取当前时间戳
        long currentTime = System.currentTimeMillis();
        //订单过期时间 30分钟
        long expireTime = currentTime + 30 * 60 * 1000;
        //存储订单过期时间戳
        genericRedisUtil.set(orderDto.getOrderNum()+"", expireTime, 30 * 60);

        //加入rabbitmq生成订单过期状态更改

        //调用支付
        String resultPay = paySimpleFactory.pay(orderDto.getPaymentMethod());
        return resultPay;

    }


    /**
     *
     * @param sourceType 业务编码
     * @return
     */
    public String getOrderNum(String sourceType) {
        String orderNo = null;
        try {
            //获取当前时间
            Date currentTime = new Date();
            //格式化当前时间为【年的后2位+月+日】
            String originDateStr = new SimpleDateFormat("yyMMdd").format(currentTime);
            //计算当前时间走过的秒
            Date startTime = new SimpleDateFormat("yyyyMMdd").parse(new SimpleDateFormat("yyyyMMdd").format(currentTime));

            long differSecond = (currentTime.getTime() - startTime.getTime()) / 1000;
            //获取【年的后2位+月+日+秒】，秒的长度不足补充0
            String yyMMddSecond = originDateStr + StringUtils.leftPad(String.valueOf(differSecond), 5, '0');

            //获取【业务编码】【年的后2位+月+日+秒】，作为自增key；
            String prefixOrder = sourceType + "" + yyMMddSecond;
//            //通过key，采用redis自增函数，实现单秒自增；不同的key，从0开始自增，同时设置60秒过期
//            Long incrId=genericRedisUtil.setIncrement(prefixOrder,1L);
//            genericRedisUtil.expire(prefixOrder,60L);//设置过期时间
            //生成订单编号
            orderNo = prefixOrder + StringUtils.leftPad(String.valueOf(16L), 4, '0');
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
        return orderNo;
    }

    @Override
    public ConcurrentHashMap<String, Object> getPagePayCode(String payType) {
        ConcurrentHashMap<String, Object> concurrentHashMap = new ConcurrentHashMap<>();
        //获取订单号
        String orderNum = this.getOrderNum("2");
        //获取预支付页面
        String pagePayCode = paySimpleFactory.pay(payType);
        concurrentHashMap.put("orderNum", orderNum);
        concurrentHashMap.put("pagePayCode", pagePayCode);
        return concurrentHashMap;
    }

    @Override
    public List<OrderVo> orderLists() {

        List<Order> list = this.list();
        if(Objects.nonNull(list) && !list.isEmpty()){
            return list.stream().map(PaymentConverter::toVo).toList();
        }

        return List.of();
    }
}

package com.example.payment.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.example.payment.entity.Order;
import com.example.payment.entity.Transaction;
import com.example.payment.enums.OrderTypeEnum;
import com.example.payment.mapper.TransactionMapper;
import com.example.payment.service.TransactionService;
import com.example.payment.strategy.TransactionProcessorStrategy;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.UUID;

/**
 * 交易记录服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TransactionServiceImpl extends ServiceImpl<TransactionMapper, Transaction> implements TransactionService {

    private final Map<String, TransactionProcessorStrategy> transactionProcessors;

    @Override
    public void createTransaction(Order order) {
        String orderType = order.getOrderType();
        log.info("开始处理交易，订单类型: '{}' (长度: {})", orderType, orderType != null ? orderType.length() : "null");
        log.info("可用的处理器keys: {}", transactionProcessors.keySet());

        // 详细调试Map中的每个key
        transactionProcessors.keySet().forEach(key ->
            log.info("Map中的key: '{}' (长度: {})", key, key.length()));

        // 获取对应的处理策略 - 使用订单类型作为key，而不是类名
        TransactionProcessorStrategy processor = transactionProcessors.get(orderType);
        log.info("根据key '{}' 获取到的processor: {}", orderType, processor != null ? processor.getClass().getSimpleName() : "null");

        if (processor == null) {
            throw new IllegalArgumentException("不支持的订单类型: '" + orderType +
                    "'，可用类型: " + transactionProcessors.keySet());
        }

        // 使用策略处理交易数据
        Transaction transaction = processor.processTransaction(order);

        // 设置通用字段
        transaction.setId(UUID.randomUUID().toString());

        // 保存交易记录
        boolean result = this.save(transaction);
        if(result){
            log.info("交易记录保存成功");
        }
    }
}

package com.example.payment.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.example.payment.entity.Order;
import com.example.payment.entity.Transaction;
import com.example.payment.enums.OrderTypeEnum;
import com.example.payment.mapper.TransactionMapper;
import com.example.payment.service.TransactionService;
import com.example.payment.strategy.TransactionProcessorStrategy;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.UUID;

/**
 * 交易记录服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TransactionServiceImpl extends ServiceImpl<TransactionMapper, Transaction> implements TransactionService {

    private final Map<String, TransactionProcessorStrategy> transactionProcessors;

    @Override
    public void createTransaction(Order order) {
        log.info("开始处理交易，订单类型: {}", order.getOrderType());
        log.info("可用的处理器: {}", transactionProcessors.keySet());

        // 获取对应的处理策略
        TransactionProcessorStrategy processor = transactionProcessors.get("rechargeTransactionProcessor");
        if (processor == null) {
            throw new IllegalArgumentException("不支持的订单类型: " + order.getOrderType() +
                    "，可用类型: " + transactionProcessors.keySet());
        }

        // 使用策略处理交易数据
        Transaction transaction = processor.processTransaction(order);

        // 设置通用字段
        transaction.setId(UUID.randomUUID().toString());

        // 保存交易记录
        boolean result = this.save(transaction);
        if(result){
            log.info("交易记录保存成功");
        }
    }
}

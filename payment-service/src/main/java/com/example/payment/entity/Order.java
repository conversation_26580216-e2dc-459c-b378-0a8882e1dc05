package com.example.payment.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.example.common.entity.BaseEntity;
import lombok.*;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * 订单实体类
 * 对应数据库表：orders
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("orders")
@Builder
public class Order extends BaseEntity {

    /**
     * 订单ID
     */
    @TableId(type = IdType.AUTO)
    private String id;

    /**
     * 用户ID
     */
    private Long userId;



    private String orderNum;


    private String outTradeNo;

    /**
     * 订单类型：vip-购买VIP，recharge-充值
     */
    private String orderType;

    /**
     * 订单金额
     */
    private BigDecimal amount;

    /**
     * 支付方式ID
     */
    private String paymentMethodId;


    private String paymentMethod;

    /**
     * 订单状态：pending-待支付，paid-已支付，cancelled-已取消，failed-支付失败
     */
    private String status;

    /**
     * 支付时间
     */
    private LocalDateTime payTime;



    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    /**
     * 取消时间
     */
    private LocalDateTime cancelTime;

    /**
     * 订单过期时间
     */
    private LocalDateTime expireTime;
}

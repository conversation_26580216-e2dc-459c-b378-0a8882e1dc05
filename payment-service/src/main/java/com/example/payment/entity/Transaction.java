package com.example.payment.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.example.common.entity.BaseEntity;
import lombok.*;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 交易记录实体类
 * 对应数据库表：transactions
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("transactions")
@Builder
public class Transaction extends BaseEntity {

    /**
     * 交易ID
     */
    @TableId(type = IdType.INPUT)
    private String id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 关联订单ID
     */
    private String orderId;

    /**
     * 交易类型：recharge-充值，consume-消费，vip-购买VIP，refund-退款
     */
    private String type;

    /**
     * 交易金额（分为单位）
     */
    private Long amount;

    /**
     * 交易后余额（分为单位）
     */
    private Long balance;

    /**
     * 交易描述
     */
    private String description;

    /**
     * 支付方式ID
     */
    private String paymentMethodId;

    /**
     * 交易状态：success-成功，pending-处理中，failed-失败
     */
    private String status;

    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;
}

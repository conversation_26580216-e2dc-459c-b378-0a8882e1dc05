package com.example.payment.config;

import com.example.payment.strategy.TransactionProcessorStrategy;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 交易策略配置类
 * 自动注册所有交易处理策略到Map中，便于根据订单类型快速查找对应的处理器
 */
@Configuration
public class TransactionStrategyConfig {

    /**
     * 将所有TransactionProcessorStrategy实现类注册到Map中
     * key: 订单类型代码
     * value: 对应的处理策略实例
     */
    @Bean
    public Map<String, TransactionProcessorStrategy> transactionProcessors(
            List<TransactionProcessorStrategy> strategies) {
        return strategies.stream()
                .collect(Collectors.toMap(
                        TransactionProcessorStrategy::getSupportedOrderType,
                        Function.identity()
                ));
    }
}

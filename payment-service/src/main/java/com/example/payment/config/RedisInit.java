package com.example.payment.config;

import com.example.common.utils.GenericRedisUtil;
import com.example.payment.entity.vo.VipPlanVo;
import com.example.payment.enums.VipDescEnum;
import com.example.payment.mapper.VipPlanMapper;
import com.example.payment.service.VipPlanService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.List;

/**
 * <AUTHOR>
 * Create by 2025/7/23 15:24
 * desc 初始化缓存
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class RedisInit {

    private final VipPlanMapper vipPlanMapper;
    private final GenericRedisUtil genericRedisUtil;

    /**
     * 初始化vip套餐以及特性列表
     */
    @PostConstruct
    public void initVipPlan() {
        //默认先删除
        genericRedisUtil.delete(VipDescEnum.VIP_PLAN.getDesc());
        try {
            List<VipPlanVo> allVipPlansFeatures = vipPlanMapper.getAllVipPlansFeatures();
            if(allVipPlansFeatures.isEmpty()){
                log.warn("暂无vip套餐数据，请先添加数据");
                return;
            }
            boolean result = genericRedisUtil.lSet(VipDescEnum.VIP_PLAN.getDesc(), allVipPlansFeatures);
            if(result){
                log.info("vip套餐以及特性列表初始化成功");
            }
        } catch (Exception e) {
            log.error("vip套餐以及特性列表初始化失败", e);
        }
    }
}
